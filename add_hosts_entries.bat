@echo off
setlocal enabledelayedexpansion

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 此脚本需要管理员权限才能修改hosts文件。
    echo 请右键点击此文件，选择"以管理员身份运行"。
    pause
    exit /b 1
)

:: 设置hosts文件路径
set "hosts_file=%SystemRoot%\System32\drivers\etc\hosts"

:: 要添加的hosts条目
set "entries[0]=127.0.0.1 idb.iobit.com"
set "entries[1]=127.0.0.1 asc55.iobit.com"
set "entries[2]=127.0.0.1 is360.iobit.com"
set "entries[3]=127.0.0.1 asc.iobit.com"
set "entries[4]=127.0.0.1 pf.iobit.com"

echo 正在检查并添加hosts条目...
echo.

:: 备份原始hosts文件
copy "%hosts_file%" "%hosts_file%.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%" >nul 2>&1
if %errorLevel% equ 0 (
    echo 已创建hosts文件备份。
) else (
    echo 警告: 无法创建备份文件。
)

:: 检查并添加每个条目
for /l %%i in (0,1,4) do (
    set "current_entry=!entries[%%i]!"
    
    :: 检查条目是否已存在
    findstr /c:"!current_entry!" "%hosts_file%" >nul 2>&1
    if !errorLevel! equ 0 (
        echo [已存在] !current_entry!
    ) else (
        :: 添加条目到hosts文件
        echo !current_entry! >> "%hosts_file%"
        if !errorLevel! equ 0 (
            echo [已添加] !current_entry!
        ) else (
            echo [失败] 无法添加 !current_entry!
        )
    )
)

echo.
echo 操作完成！

:: 刷新DNS缓存
echo 正在刷新DNS缓存...
ipconfig /flushdns >nul 2>&1
if %errorLevel% equ 0 (
    echo DNS缓存已刷新。
) else (
    echo 警告: DNS缓存刷新失败。
)

echo.
echo 所有操作已完成。按任意键退出...
pause >nul
