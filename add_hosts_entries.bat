@echo off
setlocal enabledelayedexpansion

:: Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges to modify the hosts file.
    echo Please right-click this file and select "Run as administrator".
    pause
    exit /b 1
)

:: Set hosts file path
set "hosts_file=%SystemRoot%\System32\drivers\etc\hosts"

:: Hosts entries to add
set "entries[0]=127.0.0.1 idb.iobit.com"
set "entries[1]=127.0.0.1 asc55.iobit.com"
set "entries[2]=127.0.0.1 is360.iobit.com"
set "entries[3]=127.0.0.1 asc.iobit.com"
set "entries[4]=127.0.0.1 pf.iobit.com"

echo Checking and adding hosts entries...
echo.

:: Backup original hosts file
copy "%hosts_file%" "%hosts_file%.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Hosts file backup created.
) else (
    echo Warning: Unable to create backup file.
)

:: Check and add each entry
for /l %%i in (0,1,4) do (
    set "current_entry=!entries[%%i]!"

    :: Check if entry already exists
    findstr /c:"!current_entry!" "%hosts_file%" >nul 2>&1
    if !errorLevel! equ 0 (
        echo [EXISTS] !current_entry!
    ) else (
        :: Add entry to hosts file
        echo !current_entry! >> "%hosts_file%"
        if !errorLevel! equ 0 (
            echo [ADDED] !current_entry!
        ) else (
            echo [FAILED] Unable to add !current_entry!
        )
    )
)

echo.
echo Operation completed!

:: Flush DNS cache
echo Flushing DNS cache...
ipconfig /flushdns >nul 2>&1
if %errorLevel% equ 0 (
    echo DNS cache flushed.
) else (
    echo Warning: DNS cache flush failed.
)

echo.
echo All operations completed. Press any key to exit...
pause >nul
